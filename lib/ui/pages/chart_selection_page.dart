import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../models/birth_data.dart';
import '../../models/chart_data.dart';
import '../../models/chart_type.dart';
import '../../ui/AppTheme.dart';
import '../../viewmodels/chart_viewmodel.dart';
import '../../viewmodels/files_viewmodel.dart';
import '../../viewmodels/recent_charts_viewmodel.dart';
// 暫時註釋掉，等待創建正確的路徑
// import '../main/firdaria_page_route.dart';
import 'chart_page.dart';

// 排序字段枚舉
enum SortFieldType { id, name, birthDate, birthPlace, createdAt }

class ChartSelectionPage extends StatefulWidget {
  final BirthData? primaryPerson; // 可以為 null，讓用戶在頁面中選擇
  final List<BirthData>? allPeople;
  final BirthData? secondaryPerson; // 第二個人參數
  final ChartType? initialChartType; // 初始星盤類型
  final bool isChangingChartType; // 是否為切換星盤類型模式
  final DateTime? specificDate; // 特定日期

  const ChartSelectionPage({
    super.key,
    this.primaryPerson, // 改為可選參數
    this.allPeople,
    this.secondaryPerson,
    this.initialChartType,
    this.isChangingChartType = false,
    this.specificDate,
  });

  @override
  State<ChartSelectionPage> createState() => _ChartSelectionPageState();
}

class _ChartSelectionPageState extends State<ChartSelectionPage> {
  // 選擇的星盤類型
  ChartType _selectedChartType = ChartType.natal;

  // 主要人物（可以為 null，需要用戶選擇）
  BirthData? _primaryPerson;

  // 選擇的第二個人（用於合盤）
  BirthData? _selectedSecondaryPerson;

  // 選擇的特定日期（用於推運和天象盤）
  DateTime _selectedDate = DateTime.now();

  // 搜尋關鍵字
  String _searchQuery = '';

  // 星盤類型對應的圖標
  final Map<ChartType, IconData> _chartTypeIcons = {
    ChartType.natal: Icons.person,
    ChartType.transit: Icons.access_time,
    ChartType.synastry: Icons.people,
    ChartType.composite: Icons.merge_type,
    ChartType.davison: Icons.compare_arrows,
    ChartType.marks: Icons.connect_without_contact,
    ChartType.secondaryProgression: Icons.timeline,
    ChartType.tertiaryProgression: Icons.show_chart,
    ChartType.solarArcDirection: Icons.wb_sunny,
    ChartType.solarReturn: Icons.replay,
    ChartType.lunarReturn: Icons.nightlight_round,
    // ChartType.electional: Icons.event_available,
    ChartType.horary: Icons.help_outline,
    ChartType.event: Icons.event,
    ChartType.firdaria: Icons.hourglass_full,
    // ChartType.fixedStars: Icons.star,
    // ChartType.harmonic: Icons.waves,
    // ChartType.draconic: Icons.change_circle,
    // ChartType.localSpace: Icons.location_on,
    ChartType.mundane: Icons.public,
    ChartType.synastrySecondary: Icons.compare,
    ChartType.synastryTertiary: Icons.compare_arrows,
    ChartType.compositeSecondary: Icons.merge,
    ChartType.compositeTertiary: Icons.merge_type,
    ChartType.davisonSecondary: Icons.compare,
    ChartType.davisonTertiary: Icons.compare_arrows,
    ChartType.marksSecondary: Icons.connect_without_contact,
    ChartType.marksTertiary: Icons.connecting_airports,
  };

  @override
  void initState() {
    super.initState();

    // 初始化主要人物（可能為 null）
    _primaryPerson = widget.primaryPerson;

    // 如果有初始星盤類型，則設置為預設選擇
    if (widget.initialChartType != null) {
      _selectedChartType = widget.initialChartType!;
    }

    // 如果有第二個人參數，自動設置
    if (widget.secondaryPerson != null) {
      // 設置第二個人
      _selectedSecondaryPerson = widget.secondaryPerson;

      // 如果沒有初始星盤類型，設置預設合盤類型
      if (widget.initialChartType == null) {
        // 預設選擇合盤類型
        _selectedChartType = ChartType.synastry;
      }
    }

    // 如果有特定日期參數，設置選擇的日期
    if (widget.specificDate != null) {
      _selectedDate = widget.specificDate!;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('選擇星盤類型'),
        actions: [
          // 搜尋按鈕
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // 顯示搜尋對話框
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('搜尋星盤類型'),
                  content: TextField(
                    autofocus: true,
                    decoration: const InputDecoration(
                      hintText: '輸入星盤名稱關鍵字',
                      prefixIcon: Icon(Icons.search),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                      Navigator.pop(context);
                    },
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('取消'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 人物信息卡片區域 - 使用可滾動的容器
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.25, // 限制最大高度為螢幕高度的25%
              ),
              child: SingleChildScrollView(
                child: _buildPersonsSection(),
              ),
            ),

            // 星盤類型選擇區域
            Expanded(
              child: _searchQuery.isNotEmpty
                  ? _buildSearchResultsView()
                  : _buildCompactChartsView(),
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  // 構建人物信息卡片區域
  Widget _buildPersonsSection() {
    return Column(
      mainAxisSize: MainAxisSize.min, // 確保列只佔用必要的空間
      children: [
        // 主要人物卡片或選擇按鈕
        _primaryPerson != null
            ? _buildCompactPersonCard(
                person: _primaryPerson!,
                isPrimary: true,
                onChangePerson: _changePrimaryPerson,
              )
            : _buildCompactSelectPrimaryPersonCard(),

        // 如果需要第二個人或已經選擇了第二個人，顯示第二個人卡片
        if (_selectedChartType.requiresTwoPersons || _selectedSecondaryPerson != null) ...[
          const SizedBox(height: 8),

          // 如果已經選擇了第二個人，顯示其卡片，否則顯示選擇按鈕
          _selectedSecondaryPerson != null
              ? _buildCompactPersonCard(
                  person: _selectedSecondaryPerson!,
                  isPrimary: false,
                  onChangePerson: _changeSecondaryPerson,
                )
              : _buildCompactSelectSecondaryPersonCard(),

          // 如果已經選擇了兩個人，顯示交換按鈕
          if (_selectedSecondaryPerson != null && _primaryPerson != null) ...[
            const SizedBox(height: 8),
            _buildCompactSwapPersonsButton(),
          ],
        ],
        // 添加底部空間
        const SizedBox(height: 8),
      ],
    );
  }

  // 構建線湊的人物卡片
  Widget _buildCompactPersonCard({
    required BirthData person,
    required bool isPrimary,
    required VoidCallback onChangePerson,
  }) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onChangePerson,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            children: [
              // 人物標籤
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: isPrimary ? AppColors.royalIndigo.withOpacity(0.1) : Colors.pinkAccent.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  isPrimary ? '主要' : '次要',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: isPrimary ? AppColors.royalIndigo : Colors.pinkAccent,
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // 人物信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 人物名稱
                    Text(
                      person.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    // 出生日期和地點
                    Text(
                      '${_formatDateTime(person.birthDate)} | ${person.birthPlace}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.textMedium,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // 更改按鈕
              IconButton(
                icon: const Icon(Icons.edit, size: 16),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: onChangePerson,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 構建線湊的選擇主要人物卡片
  Widget _buildCompactSelectPrimaryPersonCard() {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: _changePrimaryPerson,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            children: [
              // 人物標籤
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '主要',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // 選擇按鈕文字
              const Expanded(
                child: Text(
                  '選擇主要人物',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textMedium,
                  ),
                ),
              ),

              // 選擇按鈕
              const Icon(Icons.person_add, size: 16, color: AppColors.royalIndigo),
            ],
          ),
        ),
      ),
    );
  }

  // 構建線湊的選擇第二個人卡片
  Widget _buildCompactSelectSecondaryPersonCard() {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: _selectSecondaryPerson,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            children: [
              // 人物標籤
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.pinkAccent.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '次要',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.pinkAccent,
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // 選擇按鈕文字
              const Expanded(
                child: Text(
                  '選擇第二個人',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textMedium,
                  ),
                ),
              ),

              // 選擇按鈕
              const Icon(Icons.person_add, size: 16, color: Colors.pinkAccent),
            ],
          ),
        ),
      ),
    );
  }

  // 構建線湊的交換人物按鈕
  Widget _buildCompactSwapPersonsButton() {
    return Card(
      elevation: 1,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: _swapPersons,
        borderRadius: BorderRadius.circular(12),
        child: const Padding(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.swap_horiz, color: AppColors.royalIndigo, size: 16),
              SizedBox(width: 8),
              Text(
                '交換主次要人物',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 構建搜尋結果視圖
  Widget _buildSearchResultsView() {
    // 過濾符合搜尋關鍵字的星盤類型
    final List<ChartType> filteredChartTypes = ChartType.values
        .where((type) => type.name.toLowerCase().contains(_searchQuery.toLowerCase()))
        .toList();

    if (filteredChartTypes.isEmpty) {
      return const Center(
        child: Text('沒有符合的搜尋結果'),
      );
    }

    // 取得收藏的星盤類型
    final recentChartsViewModel = Provider.of<RecentChartsViewModel>(context);
    final favoriteChartTypes = recentChartsViewModel.favoriteCharts
        .map((record) => record.chartType)
        .toList();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜尋結果標題
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
            child: Row(
              children: [
                const Icon(Icons.search, size: 18),
                const SizedBox(width: 8),
                Text(
                  '搜尋: "$_searchQuery"',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                // 清除搜尋按鈕
                IconButton(
                  icon: const Icon(Icons.clear, size: 18),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                ),
              ],
            ),
          ),

          // 搜尋結果網格
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3, // 每行顯示3個卡片
                childAspectRatio: 1.2, // 稍微橫向的矩形卡片，減少高度
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: filteredChartTypes.length,
              itemBuilder: (context, index) {
                final chartType = filteredChartTypes[index];
                final isSelected = _selectedChartType == chartType;
                final isFavorite = favoriteChartTypes.contains(chartType);

                // 根據分類確定顏色
                Color categoryColor;
                if (chartType == ChartType.natal) {
                  categoryColor = Colors.blue;
                } else if (chartType.isRelationshipChart) {
                  categoryColor = Colors.pink;
                } else if (chartType.isPredictiveChart) {
                  categoryColor = Colors.purple;
                } else if (chartType.isReturnChart) {
                  categoryColor = Colors.orange;
                } else if (chartType.isEventChart) {
                  categoryColor = Colors.green;
                } else if (chartType.isSpecialChart) {
                  categoryColor = Colors.teal;
                } else {
                  categoryColor = AppColors.royalIndigo;
                }

                return _buildCompactChartTypeCard(
                  chartType,
                  isSelected,
                  categoryColor,
                  isFavorite,
                  recentChartsViewModel,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // 構建底部欄
  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 只顯示日期選擇器，移除第二個人選擇器
          if (_selectedChartType.requiresSpecificDate)
            _buildDateSelector(),

          const SizedBox(height: 16),

          // 查看星盤按鈕
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _validateAndViewChart,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.royalIndigo,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                '查看星盤',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 移除了底部的第二個人選擇器，只保留人物卡片區域中的選擇功能

  // 構建日期選擇器
  Widget _buildDateSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '選擇日期',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.textLight),
          ),
          child: ListTile(
            title: Text(_formatDateTime(_selectedDate)),
            trailing: const Icon(Icons.calendar_today),
            onTap: () async {
              final pickedDate = await showDatePicker(
                context: context,
                initialDate: _selectedDate,
                firstDate: DateTime(1900),
                lastDate: DateTime(2100),
              );

              if (pickedDate != null) {
                final pickedTime = await showTimePicker(
                  context: context,
                  initialTime: TimeOfDay.fromDateTime(_selectedDate),
                );

                if (pickedTime != null && context.mounted) {
                  setState(() {
                    _selectedDate = DateTime(
                      pickedDate.year,
                      pickedDate.month,
                      pickedDate.day,
                      pickedTime.hour,
                      pickedTime.minute,
                    );
                  });
                }
              }
            },
          ),
        ),
      ],
    );
  }

  // 驗證並查看星盤
  void _validateAndViewChart() {
    // 檢查是否選擇了主要人物
    if (_primaryPerson == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('請先選擇主要人物')),
      );
      return;
    }

    // 檢查是否需要第二個人但未選擇
    if (_selectedChartType.requiresTwoPersons &&
        _selectedSecondaryPerson == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('請選擇第二個人')),
      );
      return;
    }

    // 法達盤也使用 ChartPage，因為我們已經將法達盤功能整合到 ChartPage 中

    // 創建 ChartData 對象
    final chartData = ChartData(
      chartType: _selectedChartType,
      primaryPerson: _primaryPerson!, // 已經驗證不為 null
      secondaryPerson: _selectedSecondaryPerson,
      specificDate:
          _selectedChartType.requiresSpecificDate ? _selectedDate : null,
    );

    // 如果是切換星盤類型模式，返回 ChartData 而不導航到新頁面
    if (widget.isChangingChartType) {
      Navigator.pop(context, chartData);
    } else {
      // 導航到星盤頁面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) => ChartViewModel.withChartData(initialChartData: chartData),
            child: ChartPage(chartData: chartData),
          ),
        ),
      );
    }
  }

  // 更改主要人物
  void _changePrimaryPerson() async {
    final filesViewModel = Provider.of<FilesViewModel>(context, listen: false);
    await filesViewModel.loadBirthData();

    if (context.mounted) {
      // 過濾掉第二個人（如果有）
      final filteredList = _selectedSecondaryPerson != null
          ? filesViewModel.birthDataList.where((data) => data.id != _selectedSecondaryPerson!.id).toList()
          : filesViewModel.birthDataList;

      final BirthData? selectedPerson = await _showPersonSelectionDialog(
        filteredList,
        title: '選擇主要人物',
        buttonColor: AppColors.royalIndigo,
      );

      if (selectedPerson != null && mounted) {
        setState(() {
          _primaryPerson = selectedPerson;
        });
      }
    }
  }

  // 更改次要人物
  void _changeSecondaryPerson() async {
    final filesViewModel = Provider.of<FilesViewModel>(context, listen: false);
    await filesViewModel.loadBirthData();

    if (context.mounted) {
      // 過濾掉主要人物（如果有）
      final filteredList = _primaryPerson != null
          ? filesViewModel.birthDataList
              .where((data) => data.id != _primaryPerson!.id)
              .toList()
          : filesViewModel.birthDataList;

      final BirthData? selectedPerson = await _showPersonSelectionDialog(
        filteredList,
        title: '選擇次要人物',
        buttonColor: Colors.pinkAccent,
      );

      if (selectedPerson != null && mounted) {
        setState(() {
          _selectedSecondaryPerson = selectedPerson;
        });
      }
    }
  }

  // 選擇第二個人
  void _selectSecondaryPerson() async {
    final filesViewModel = Provider.of<FilesViewModel>(context, listen: false);
    await filesViewModel.loadBirthData();

    if (context.mounted) {
      // 過濾掉主要人物（如果有）
      final filteredList = _primaryPerson != null
          ? filesViewModel.birthDataList
              .where((data) => data.id != _primaryPerson!.id)
              .toList()
          : filesViewModel.birthDataList;

      final BirthData? selectedPerson = await _showPersonSelectionDialog(
        filteredList,
        title: '選擇次要人物',
        buttonColor: Colors.pinkAccent,
      );

      if (selectedPerson != null && mounted) {
        setState(() {
          _selectedSecondaryPerson = selectedPerson;
        });
      }
    }
  }

  // 交換主次要人物
  void _swapPersons() {
    if (_selectedSecondaryPerson == null || _primaryPerson == null) return;

    setState(() {
      final temp = _primaryPerson;
      _primaryPerson = _selectedSecondaryPerson!;
      _selectedSecondaryPerson = temp;
    });
  }

  // 構建緊湊的星盤類型視圖
  Widget _buildCompactChartsView() {
    // 按分類組織星盤類型，但不顯示分類標題
    final Map<String, List<ChartType>> categorizedCharts = {
      '特殊星盤': [ChartType.mundane, ChartType.firdaria, ChartType.equinoxSolstice],
      '基本盤': [ChartType.natal, ChartType.transit],
      '合盤': [ChartType.synastry, ChartType.composite, ChartType.davison, ChartType.marks],
      '推運': [ChartType.secondaryProgression, ChartType.tertiaryProgression, ChartType.solarArcDirection],
      '返照盤': [ChartType.solarReturn, ChartType.lunarReturn],
      '合盤推運': [
        ChartType.synastrySecondary, ChartType.synastryTertiary,
        ChartType.compositeSecondary, ChartType.compositeTertiary,
        ChartType.davisonSecondary, ChartType.davisonTertiary,
        ChartType.marksSecondary, ChartType.marksTertiary,
      ],
    };

    // 將所有星盤類型展平為一個列表
    final List<ChartType> allChartTypes = [];
    categorizedCharts.forEach((category, types) {
      allChartTypes.addAll(types);
    });

    // 將收藏的星盤類型放在最前面
    final recentChartsViewModel = Provider.of<RecentChartsViewModel>(context);
    final favoriteChartTypes = recentChartsViewModel.favoriteCharts
        .map((record) => record.chartType)
        .toList();

    // 將收藏的星盤類型從列表中移除，以避免重複
    allChartTypes.removeWhere((type) => favoriteChartTypes.contains(type));

    // 將收藏的星盤類型放在最前面
    final List<ChartType> sortedChartTypes = [...favoriteChartTypes, ...allChartTypes];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4, // 每行顯示3個卡片
          childAspectRatio: 1.2, // 稍微橫向的矩形卡片，減少高度
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: sortedChartTypes.length,
        itemBuilder: (context, index) {
          final chartType = sortedChartTypes[index];
          final isSelected = _selectedChartType == chartType;
          final isFavorite = favoriteChartTypes.contains(chartType);

          // 根據分類確定顏色
          Color categoryColor;
          if (chartType == ChartType.natal) {
            categoryColor = Colors.blue;
          } else if (chartType.isRelationshipChart) {
            categoryColor = Colors.pink;
          } else if (chartType.isPredictiveChart) {
            categoryColor = Colors.purple;
          } else if (chartType.isReturnChart) {
            categoryColor = Colors.orange;
          } else if (chartType.isEventChart) {
            categoryColor = Colors.green;
          } else if (chartType.isSpecialChart) {
            categoryColor = Colors.teal;
          } else {
            categoryColor = AppColors.royalIndigo;
          }

          return _buildCompactChartTypeCard(
            chartType,
            isSelected,
            categoryColor,
            isFavorite,
            recentChartsViewModel,
          );
        },
      ),
    );
  }

  // 構建線湊的星盤類型卡片
  Widget _buildCompactChartTypeCard(
    ChartType chartType,
    bool isSelected,
    Color categoryColor,
    bool isFavorite,
    RecentChartsViewModel viewModel,
  ) {
    return Card(
      elevation: isSelected ? 4 : 1,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected ? categoryColor : Colors.transparent,
          width: isSelected ? 2.0 : 0.0,
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedChartType = chartType;

            // 如果切換到不需要第二個人的星盤類型，清除選擇的第二個人
            if (!chartType.requiresTwoPersons) {
              _selectedSecondaryPerson = null;
            }
          });
        },
        onLongPress: () {
          // 長按切換收藏狀態
          // 檢查是否選擇了主要人物
          if (_primaryPerson == null) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('請先選擇主要人物')),
            );
            return;
          }

          // 先創建 ChartData 對象
          final chartData = ChartData(
            chartType: chartType,
            primaryPerson: _primaryPerson!,
            secondaryPerson: _selectedSecondaryPerson,
            specificDate: _selectedChartType.requiresSpecificDate ? _selectedDate : null,
          );

          // 將星盤添加到最近使用的記錄中
          viewModel.addOrUpdateRecentChart(chartData);

          // 顯示提示
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('已添加${chartType.name}到最近使用的記錄中')),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // 星盤卡片內容
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 4.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min, // 確保列使用最小高度
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 圖標
                    Icon(
                      _chartTypeIcons[chartType] ?? Icons.star,
                      color: categoryColor,
                      size: 20, // 縮小圖標
                    ),
                    const SizedBox(height: 2), // 減少間距
                    // 星盤名稱
                    Text(
                      chartType.name,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 12, // 縮小字體
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        color: isSelected ? categoryColor : AppColors.textDark,
                      ),
                      maxLines: 1, // 限制為一行
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),

            // 收藏圖標
            if (isFavorite)
              const Positioned(
                top: 1,
                right: 1,
                child: Icon(
                  Icons.star,
                  color: Colors.amber,
                  size: 12, // 縮小圖標
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 顯示人物選擇對話框
  Future<BirthData?> _showPersonSelectionDialog(
    List<BirthData> birthDataList, {
    required String title,
    required Color buttonColor,
  }) async {
    // 從 SharedPreferences 加載排序設置
    final prefs = await SharedPreferences.getInstance();
    final sortFieldIndex = prefs.getInt('personSelectionSortField') ?? SortFieldType.name.index;
    final isAscendingPref = prefs.getBool('personSelectionIsAscending') ?? true;

    // 排序方式
    SortFieldType currentSortField = SortFieldType.values[sortFieldIndex]; // 使用儲存的排序方式
    bool isAscending = isAscendingPref; // 使用儲存的排序方向

    // 搜尋關鍵字
    String searchQuery = '';

    // 排序函數
    void sortList(List<BirthData> list, SortFieldType field, bool ascending) {
      switch (field) {
        case SortFieldType.id:
          list.sort((a, b) => ascending ? a.id.compareTo(b.id) : b.id.compareTo(a.id));
          break;
        case SortFieldType.name:
          list.sort((a, b) => ascending ? a.name.compareTo(b.name) : b.name.compareTo(a.name));
          break;
        case SortFieldType.birthDate:
          list.sort((a, b) => ascending ? a.birthDate.compareTo(b.birthDate) : b.birthDate.compareTo(a.birthDate));
          break;
        case SortFieldType.birthPlace:
          list.sort((a, b) => ascending ? a.birthPlace.compareTo(b.birthPlace) : b.birthPlace.compareTo(a.birthPlace));
          break;
        case SortFieldType.createdAt:
          list.sort((a, b) => ascending ? a.createdAt.compareTo(b.createdAt) : b.createdAt.compareTo(a.createdAt));
          break;
      }
    }

    // 初始排序
    sortList(birthDataList, currentSortField, isAscending);

    return showDialog<BirthData>(
      context: context,
      builder: (context) {
        // 使用 StatefulBuilder 以便在對話框內更新狀態
        return StatefulBuilder(
          builder: (context, setState) {
            // 過濾列表
            final filteredList = searchQuery.isEmpty
                ? birthDataList
                : birthDataList.where((person) {
                    return person.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
                        person.birthPlace.toLowerCase().contains(searchQuery.toLowerCase()) ||
                        _formatDateTime(person.birthDate).contains(searchQuery);
                  }).toList();

            return AlertDialog(
              title: Text(title),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              content: SizedBox(
                width: double.maxFinite,
                height: MediaQuery.of(context).size.height * 0.6, // 增加高度以容納排序選項
                child: Column(
                  children: [
                    // 搜索欄
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: TextField(
                        decoration: InputDecoration(
                          hintText: '搜索人物',
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          contentPadding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        onChanged: (value) {
                          setState(() {
                            searchQuery = value;
                          });
                        },
                      ),
                    ),

                    // 排序選項
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          const Text('排序：', style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(width: 8),
                          // 排序下拉選單
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: DropdownButton<SortFieldType>(
                                isExpanded: true,
                                underline: const SizedBox(),
                                value: currentSortField,
                                items: const [
                                  DropdownMenuItem(value: SortFieldType.id, child: Text('ID')),
                                  DropdownMenuItem(value: SortFieldType.name, child: Text('姓名')),
                                  DropdownMenuItem(value: SortFieldType.birthDate, child: Text('出生日期')),
                                  DropdownMenuItem(value: SortFieldType.birthPlace, child: Text('出生地點')),
                                  DropdownMenuItem(value: SortFieldType.createdAt, child: Text('建立時間')),
                                ],
                                onChanged: (value) async {
                                  if (value != null) {
                                    // 儲存排序設置
                                    final prefs = await SharedPreferences.getInstance();
                                    await prefs.setInt('personSelectionSortField', value.index);

                                    setState(() {
                                      currentSortField = value;
                                      sortList(birthDataList, currentSortField, isAscending);
                                    });
                                  }
                                },
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          // 升/降序按鈕
                          IconButton(
                            icon: Icon(isAscending ? Icons.arrow_upward : Icons.arrow_downward),
                            onPressed: () async {
                              // 儲存排序方向
                              final prefs = await SharedPreferences.getInstance();
                              await prefs.setBool('personSelectionIsAscending', !isAscending);

                              setState(() {
                                isAscending = !isAscending;
                                sortList(birthDataList, currentSortField, isAscending);
                              });
                            },
                            tooltip: isAscending ? '升序排列' : '降序排列',
                          ),
                        ],
                      ),
                    ),

                    // 人物列表
                    Expanded(
                      child: filteredList.isEmpty
                          ? const Center(child: Text('沒有符合條件的人物'))
                          : ListView.builder(
                              shrinkWrap: true,
                              itemCount: filteredList.length,
                              itemBuilder: (context, index) {
                                final person = filteredList[index];
                                return Card(
                                  margin: const EdgeInsets.only(bottom: 8),
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                  child: ListTile(
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                    leading: CircleAvatar(
                                      backgroundColor: buttonColor,
                                      child: Text(
                                        person.name.isNotEmpty ? person.name.substring(0, 1) : '?',
                                        style: const TextStyle(color: Colors.white),
                                      ),
                                    ),
                                    title: Text(
                                      person.name,
                                      style: const TextStyle(fontWeight: FontWeight.bold),
                                    ),
                                    subtitle: Text('${_formatDateTime(person.birthDate)} | ${person.birthPlace}'),
                                    onTap: () {
                                      Navigator.pop(context, person);
                                    },
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  style: TextButton.styleFrom(foregroundColor: AppColors.textMedium),
                  child: const Text('取消'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
