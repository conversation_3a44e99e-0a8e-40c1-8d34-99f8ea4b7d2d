import '../utils/logger_utils.dart';

/// 星盤類型枚舉
enum ChartType {
  // 個人星盤
  natal('本命盤', '分析一個人的個性、天賦、命運發展'),

  // 預測類星盤
  transit('行運盤', '觀察當下行星對個人星盤的影響'),
  secondaryProgression('次限推運盤', '一天代表一年，分析內在心理成長'),
  tertiaryProgression('三限推運盤', '一天代表一個月，反映短期心理變化'),
  solarArcDirection('太陽弧推運盤', '所有行星以太陽推進度數前進，觀察重大人生變化'),

  // 返照盤類
  solarReturn('太陽返照盤', '太陽回到出生位置時的星盤，用於年度運勢分析'),
  lunarReturn('月亮返照盤', '月亮回到出生位置時的星盤，用於月度運勢分析'),

  // 合盤類
  synastry('比較盤', '分析兩人星盤間的互動關係'),
  composite('組合盤', '將兩人星盤取中點，產生新的關係盤'),
  davison('時空中點盤', '以兩人生日取算術平均，推算新時間與地點建立星盤'),
  marks('馬克思盤', '基於兩人本命盤的特殊計算方法，反映關係的深層動力'),

  // 比較盤推運
  synastrySecondary('比較次限盤', '分析一方次限推運對另一方本命盤的影響'),
  synastryTertiary('比較三限盤', '分析一方三限推運對另一方本命盤的影響'),

  // 組合盤推運
  compositeSecondary('組合次限盤', '分析組合盤的次限推運，觀察關係長期發展'),
  compositeTertiary('組合三限盤', '分析組合盤的三限推運，觀察關係短期變化'),

  // 時空中點盤推運
  davisonSecondary('時空次限盤', '分析時空中點盤的次限推運，觀察關係長期發展'),
  davisonTertiary('時空三限盤', '分析時空中點盤的三限推運，觀察關係短期變化'),

  // 馬克思盤推運
  marksSecondary('馬克思次限盤', '分析馬克思盤的次限推運，觀察關係深層發展'),
  marksTertiary('馬克思三限盤', '分析馬克思盤的三限推運，觀察關係短期變化'),

  // 事件占星
  // electional('擇日盤', '用於挑選結婚、開業、旅行等最佳時間'),
  horary('卜卦盤', '針對特定問題，在提出問題當下起盤解答'),
  event('事件盤', '針對特定事件的發生時間起盤分析'),

  // 特殊星盤
  // fixedStars('恆星盤', '將本命盤與恆星搭配分析，提供宿命指引'),
  // harmonic('諧波盤', '利用數學運算找出隱藏在本命盤中的特定模式'),
  // draconic('龍頭盤', '以北交點為基準重新計算，顯示靈魂目標與業力關聯'),
  // localSpace('地平方位盤', '以方位角分析個人能量在不同地方的影響'),
  mundane('天象盤', '針對國家、政治、經濟等大環境做預測'),
  firdaria('法達盤', '古典占星時序技術，顯示人生不同階段的主導行星'),

  // 季節節氣星盤
  equinoxSolstice('二分二至圖', '顯示春分、夏至、秋分、冬至四個節氣的星盤配置與個人影響');

  final String displayName;
  final String description;

  const ChartType(this.displayName, this.description);

  // 獲取顯示名稱
  String get name => displayName;

  // 獲取描述
  String get desc => description;

  // 根據顯示名稱獲取枚舉值
  static ChartType? fromDisplayName(String displayName) {
    return ChartType.values.firstWhere(
      (type) => type.displayName == displayName,
      orElse: () => ChartType.natal,
    );
  }

  // 檢查是否為合盤類型
  bool get isRelationshipChart {
    return [
      ChartType.synastry,
      ChartType.composite,
      ChartType.davison,
      ChartType.marks,
      // 組合盤推運
      ChartType.compositeSecondary,
      ChartType.compositeTertiary,

      // 時空中點盤推運
      ChartType.davisonSecondary,
      ChartType.davisonTertiary,

      // 馬克思盤推運
      ChartType.marksSecondary,
      ChartType.marksTertiary,

      // 比較盤推運
      ChartType.synastrySecondary,
      ChartType.synastryTertiary,
    ].contains(this);
  }

  // 檢查是否為預測類型
  bool get isPredictiveChart {
    return [
      ChartType.secondaryProgression,
      ChartType.tertiaryProgression,
      ChartType.solarArcDirection,
      ChartType.transit,

      // 組合盤推運
      ChartType.compositeSecondary,
      ChartType.compositeTertiary,

      // 時空中點盤推運
      ChartType.davisonSecondary,
      ChartType.davisonTertiary,

      // 馬克思盤推運
      ChartType.marksSecondary,
      ChartType.marksTertiary,

      // 比較盤推運
      ChartType.synastrySecondary,
      ChartType.synastryTertiary,
    ].contains(this);
  }

  // 檢查是否為返照盤類型
  bool get isReturnChart {
    return [
      ChartType.solarReturn,
      ChartType.lunarReturn,
    ].contains(this);
  }

  // 檢查是否為事件類型
  bool get isEventChart {
    return [
      // ChartType.electional,
      ChartType.horary,
      ChartType.event,
    ].contains(this);
  }

  // 檢查是否為特殊類型
  bool get isSpecialChart {
    return [
      // ChartType.fixedStars,
      // ChartType.harmonic,
      // ChartType.draconic,
      // ChartType.localSpace,
      ChartType.mundane,
      ChartType.firdaria,
    ].contains(this);
  }

  // 獲取星盤類型的分類
  String get category {
    logger.i('獲取星盤類型分類: ${this.name}');

    if (this == ChartType.natal) {
      logger.d('類型: 個人星盤');
      return '個人星盤';
    }
    if (isRelationshipChart) {
      logger.d('類型: 合盤類');
      return '合盤類';
    }
    if (isPredictiveChart) {
      logger.d('類型: 預測類星盤');
      return '預測類星盤';
    }
    if (isReturnChart) {
      logger.d('類型: 返照盤類');
      return '返照盤類';
    }
    if (isEventChart) {
      logger.d('類型: 事件占星');
      return '事件占星';
    }
    if (isSpecialChart) {
      logger.d('類型: 特殊星盤');
      return '特殊星盤';
    }
    logger.w('未知的星盤類型: ${this.name}');
    return '其他';
  }

  // 檢查是否需要兩個人的數據
  bool get requiresTwoPersons {
    final result = isRelationshipChart;
    logger.i('檢查是否需要兩個人的數據: ${this.name} = $result');
    return result;
  }

  // 檢查是否需要特定日期
  bool get requiresSpecificDate {
    final result = isPredictiveChart || isReturnChart || isEventChart;
    logger.i('檢查是否需要特定日期: ${this.name} = $result');
    return result;
  }

  // 檢查是否需要當前時間
  bool get requiresCurrentTime {
    final result = [ChartType.transit].contains(this);
    logger.i('檢查是否需要當前時間: ${this.name} = $result');
    return result;
  }

  // 檢查是否為組合盤推運
  bool get isCompositeProgression {
    return [
      ChartType.compositeSecondary,
      ChartType.compositeTertiary,
    ].contains(this);
  }

  // 檢查是否為比較盤推運
  bool get isSynastryProgression {
    return [
      ChartType.synastrySecondary,
      ChartType.synastryTertiary,
    ].contains(this);
  }

  // 檢查是否為時空中點盤推運
  bool get isDavisonProgression {
    return [
      ChartType.davisonSecondary,
      ChartType.davisonTertiary,
    ].contains(this);
  }

  // 檢查是否為馬克思盤推運
  bool get isMarksProgression {
    return [
      ChartType.marksSecondary,
      ChartType.marksTertiary,
    ].contains(this);
  }
}
